import ZaloHelper from "./bot/zalo.helper.js";

/**
 * Conversation API wrapper for easy integration with web applications
 */
class ConversationAPI {
  constructor() {
    this.zalo = new ZaloHelper();
  }

  /**
   * Get paginated list of conversations for a user
   * @param {string} userId - The user's Zalo ID
   * @param {Object} options - Pagination and filtering options
   * @returns {Promise<Object>} Conversations list with pagination info
   */
  async getUserConversations(userId, options = {}) {
    try {
      const result = await this.zalo.getConversationsList(userId, options);
      return {
        success: true,
        data: result,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Get detailed conversation between two users
   * @param {string} userId - Current user's Zalo ID
   * @param {string} partnerId - Partner's Zalo ID
   * @param {Object} options - Pagination options
   * @returns {Promise<Object>} Conversation detail with messages
   */
  async getConversationMessages(userId, partnerId, options = {}) {
    try {
      const result = await this.zalo.getConversationDetail(userId, partnerId, options);
      return {
        success: true,
        data: result,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Get conversation by its database ID
   * @param {number} conversationId - Database conversation ID
   * @param {Object} options - Pagination options
   * @returns {Promise<Object>} Conversation with messages
   */
  async getConversationById(conversationId, options = {}) {
    try {
      const result = await this.zalo.getConversationById(conversationId, options);
      return {
        success: true,
        data: result,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Search conversations by message content
   * @param {string} userId - User's Zalo ID
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Search results
   */
  async searchUserConversations(userId, query, options = {}) {
    try {
      const result = await this.zalo.searchConversations(userId, query, options);
      return {
        success: true,
        data: result,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Get conversation statistics for a user
   * @param {string} userId - User's Zalo ID
   * @returns {Promise<Object>} User's conversation statistics
   */
  async getUserStats(userId) {
    try {
      // Get total conversations
      const conversationsResult = await this.zalo.getConversationsList(userId, { 
        page: 1, 
        limit: 1 
      });
      
      const totalConversations = conversationsResult.pagination.total;
      
      // Get total messages sent by user
      const totalMessagesSent = await this.zalo.prisma.message.count({
        where: {
          uidFrom: userId
        }
      });
      
      // Get total messages received by user
      const totalMessagesReceived = await this.zalo.prisma.message.count({
        where: {
          uidTo: userId
        }
      });
      
      // Get most recent conversation
      const recentConversations = await this.zalo.getConversationsList(userId, { 
        page: 1, 
        limit: 1 
      });
      
      const mostRecentConversation = recentConversations.conversations[0] || null;
      
      return {
        success: true,
        data: {
          userId: userId,
          totalConversations: totalConversations,
          totalMessagesSent: totalMessagesSent,
          totalMessagesReceived: totalMessagesReceived,
          totalMessages: totalMessagesSent + totalMessagesReceived,
          mostRecentConversation: mostRecentConversation
        },
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error.message
      };
    }
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    await this.zalo.cleanup();
  }
}

export default ConversationAPI;

// Example usage:
/*
const api = new ConversationAPI();

// Get user's conversations
const conversations = await api.getUserConversations("user123", {
  page: 1,
  limit: 20
});

// Get conversation messages
const messages = await api.getConversationMessages("user123", "partner456", {
  page: 1,
  limit: 50
});

// Search conversations
const searchResults = await api.searchUserConversations("user123", "hello");

// Get user statistics
const stats = await api.getUserStats("user123");

// Cleanup
await api.cleanup();
*/
