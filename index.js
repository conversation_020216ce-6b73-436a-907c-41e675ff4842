import ZaloHelper from "./bot/zalo.helper.js";

(async () => {
  try {
    console.log("Starting Zalo bot...");
    const zalo = new ZaloHelper();
    await zalo.requestQR();
    // const user = await zalo.findUserByPhone("0948377768");
    // await zalo.sendFriendRequest(user.uid);
    // const res = await zalo.getFriendRquest();
    // console.log(res.recommItems);
    // const item = res.recommItems.map(f => f.dataInfo.userId)[0];
    // await zalo.acceptFriendRequest(item);
    console.log("Done");
  } catch (error) {
    console.error("Error starting Zalo bot:", error);
  }
})();
