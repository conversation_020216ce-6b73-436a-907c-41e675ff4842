import ZaloHelper from "./bot/zalo.helper.js";

let zalo;

(async () => {
  try {
    console.log("Starting Zalo bot...");
    zalo = new ZaloHelper();
    await zalo.requestQR();
    // const user = await zalo.findUserByPhone("0948377768");
    // await zalo.sendFriendRequest(user.uid);
    // const res = await zalo.getFriendRquest();
    // console.log(res.recommItems);
    // const item = res.recommItems.map(f => f.dataInfo.userId)[0];
    // await zalo.acceptFriendRequest(item);
    console.log("Zalo bot started successfully");
  } catch (error) {
    console.error("Error starting Zalo bot:", error);
    if (zalo) {
      await zalo.cleanup();
    }
    process.exit(1);
  }
})();

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT. Graceful shutdown...');
  if (zalo) {
    await zalo.cleanup();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM. Graceful shutdown...');
  if (zalo) {
    await zalo.cleanup();
  }
  process.exit(0);
});
