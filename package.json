{"name": "zalo", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"build": "tsc", "start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"prisma": "^6.9.0"}, "dependencies": {"@prisma/client": "^6.9.0", "express": "^5.1.0", "jimp": "^1.6.0", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "zca-js": "^2.0.0-beta.24"}}