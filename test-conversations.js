import ZaloHelper from "./bot/zalo.helper.js";

async function testConversationMethods() {
  const zalo = new ZaloHelper();
  
  try {
    console.log("Testing conversation methods...\n");
    
    // Example user ID (replace with actual user ID from your system)
    const testUserId = "692347378021568686"; // This should be a real user ID from your messages
    
    console.log("1. Getting conversations list for user:", testUserId);
    const conversationsList = await zalo.getConversationsList(testUserId, {
      page: 1,
      limit: 10,
      includeMessageCount: true
    });
    
    console.log("Conversations found:", conversationsList.conversations.length);
    console.log("Pagination:", conversationsList.pagination);
    
    if (conversationsList.conversations.length > 0) {
      console.log("\nFirst conversation:");
      console.log(JSON.stringify(conversationsList.conversations[0], null, 2));
      
      // Test getting conversation detail
      const firstConversation = conversationsList.conversations[0];
      console.log("\n2. Getting conversation detail with partner:", firstConversation.partnerId);
      
      const conversationDetail = await zalo.getConversationDetail(
        testUserId, 
        firstConversation.partnerId,
        {
          page: 1,
          limit: 20
        }
      );
      
      console.log("Messages in conversation:", conversationDetail.messages.length);
      console.log("Pagination:", conversationDetail.pagination);
      
      if (conversationDetail.messages.length > 0) {
        console.log("\nFirst few messages:");
        conversationDetail.messages.slice(0, 3).forEach((msg, index) => {
          console.log(`Message ${index + 1}:`, {
            content: typeof msg.content === 'string' ? msg.content : '[Object/Media]',
            type: msg.type,
            isFromCurrentUser: msg.isFromCurrentUser,
            createdAt: msg.createdAt
          });
        });
      }
      
      // Test getting conversation by ID
      console.log("\n3. Getting conversation by ID:", firstConversation.id);
      const conversationById = await zalo.getConversationById(firstConversation.id, {
        page: 1,
        limit: 10
      });
      
      if (conversationById) {
        console.log("Conversation details:", {
          id: conversationById.conversation.id,
          uidFrom: conversationById.conversation.uidFrom,
          uidTo: conversationById.conversation.uidTo,
          messageCount: conversationById.conversation.messageCount
        });
      }
      
      // Test search functionality
      console.log("\n4. Searching conversations for 'hello' or any text...");
      const searchResults = await zalo.searchConversations(testUserId, "hello", {
        page: 1,
        limit: 5
      });
      
      console.log("Search results:", searchResults.conversations.length);
      if (searchResults.conversations.length > 0) {
        console.log("First search result:");
        console.log(JSON.stringify(searchResults.conversations[0], null, 2));
      }
    } else {
      console.log("No conversations found for this user.");
    }
    
  } catch (error) {
    console.error("Error testing conversation methods:", error);
  } finally {
    await zalo.cleanup();
  }
}

// Run the test
testConversationMethods();
