# Conversation API Documentation

This document describes the conversation management methods added to the ZaloHelper class.

## Overview

The conversation system allows you to:
- Retrieve a list of conversations for a specific user
- Get detailed conversation history between two users
- Search through conversation messages
- Get conversation statistics

## Methods

### 1. `getConversationsList(userId, options)`

Get a paginated list of conversations for a specific user.

**Parameters:**
- `userId` (string): The Zalo user ID
- `options` (object, optional):
  - `page` (number): Page number (default: 1)
  - `limit` (number): Items per page (default: 20)
  - `includeMessageCount` (boolean): Include message count and latest message (default: true)

**Returns:**
```javascript
{
  conversations: [
    {
      id: 1,
      partnerId: "partner_user_id",
      isUserSender: true,
      createdAt: "2025-06-15T10:00:00.000Z",
      updatedAt: "2025-06-15T15:00:00.000Z",
      messageCount: 25,
      latestMessage: {
        id: 100,
        content: "Hello there!",
        type: "TEXT",
        createdAt: "2025-06-15T15:00:00.000Z",
        isFromCurrentUser: false
      }
    }
  ],
  pagination: {
    page: 1,
    limit: 20,
    total: 5,
    totalPages: 1,
    hasNext: false,
    hasPrev: false
  }
}
```

**Example:**
```javascript
const conversations = await zalo.getConversationsList("user123", {
  page: 1,
  limit: 10
});
```

### 2. `getConversationDetail(userId, partnerId, options)`

Get detailed conversation history between two users.

**Parameters:**
- `userId` (string): Current user's Zalo ID
- `partnerId` (string): Partner's Zalo ID
- `options` (object, optional):
  - `page` (number): Page number (default: 1)
  - `limit` (number): Messages per page (default: 50)

**Returns:**
```javascript
{
  conversation: {
    id: 1,
    partnerId: "partner_user_id",
    currentUserId: "user123",
    createdAt: "2025-06-15T10:00:00.000Z",
    updatedAt: "2025-06-15T15:00:00.000Z"
  },
  messages: [
    {
      id: 1,
      content: "Hello!",
      type: "TEXT",
      mediaUrl: null,
      stickerId: null,
      msgId: "zalo_msg_123",
      msgType: "chat.text",
      isFromCurrentUser: true,
      senderUserId: "user123",
      receiverUserId: "partner_user_id",
      createdAt: "2025-06-15T10:00:00.000Z"
    }
  ],
  pagination: { /* pagination info */ }
}
```

**Example:**
```javascript
const conversation = await zalo.getConversationDetail("user123", "partner456", {
  page: 1,
  limit: 50
});
```

### 3. `getConversationById(conversationId, options)`

Get conversation details by database ID.

**Parameters:**
- `conversationId` (number): Database conversation ID
- `options` (object, optional):
  - `page` (number): Page number (default: 1)
  - `limit` (number): Messages per page (default: 50)

**Returns:**
Similar to `getConversationDetail` but includes both user IDs in the conversation object.

**Example:**
```javascript
const conversation = await zalo.getConversationById(123, {
  page: 1,
  limit: 30
});
```

### 4. `searchConversations(userId, searchQuery, options)`

Search through conversations by message content.

**Parameters:**
- `userId` (string): User's Zalo ID
- `searchQuery` (string): Text to search for
- `options` (object, optional):
  - `page` (number): Page number (default: 1)
  - `limit` (number): Results per page (default: 20)

**Returns:**
```javascript
{
  conversations: [
    {
      id: 1,
      partnerId: "partner_user_id",
      isUserSender: true,
      createdAt: "2025-06-15T10:00:00.000Z",
      updatedAt: "2025-06-15T15:00:00.000Z",
      messageCount: 25,
      matchingMessages: [
        {
          id: 10,
          content: "Hello world!",
          type: "TEXT",
          createdAt: "2025-06-15T12:00:00.000Z",
          isFromCurrentUser: true
        }
      ]
    }
  ],
  searchQuery: "hello",
  pagination: { /* pagination info */ }
}
```

**Example:**
```javascript
const results = await zalo.searchConversations("user123", "hello", {
  page: 1,
  limit: 10
});
```

## Usage Examples

### Basic Usage

```javascript
import ZaloHelper from "./bot/zalo.helper.js";

const zalo = new ZaloHelper();

try {
  // Get user's conversations
  const conversations = await zalo.getConversationsList("user123");
  console.log(`Found ${conversations.conversations.length} conversations`);

  // Get specific conversation
  if (conversations.conversations.length > 0) {
    const partnerId = conversations.conversations[0].partnerId;
    const detail = await zalo.getConversationDetail("user123", partnerId);
    console.log(`Conversation has ${detail.messages.length} messages`);
  }

  // Search conversations
  const searchResults = await zalo.searchConversations("user123", "important");
  console.log(`Found ${searchResults.conversations.length} conversations with "important"`);

} finally {
  await zalo.cleanup();
}
```

### Using the ConversationAPI Wrapper

```javascript
import ConversationAPI from "./conversation-api.js";

const api = new ConversationAPI();

try {
  // Get conversations with error handling
  const result = await api.getUserConversations("user123");
  if (result.success) {
    console.log("Conversations:", result.data.conversations);
  } else {
    console.error("Error:", result.error);
  }

  // Get user statistics
  const stats = await api.getUserStats("user123");
  if (stats.success) {
    console.log("User stats:", stats.data);
  }

} finally {
  await api.cleanup();
}
```

## Message Content Types

Messages can contain different types of content:

- **TEXT**: Simple text messages (content is a string)
- **MEDIA**: Images, videos, files (content is an object with media info)
- **STICKER**: Stickers (content is an object with sticker info)

The `content` field is stored as JSON and can be either a string or an object depending on the message type.

## Pagination

All list methods support pagination:
- `page`: Current page number (1-based)
- `limit`: Number of items per page
- `total`: Total number of items
- `totalPages`: Total number of pages
- `hasNext`: Whether there are more pages
- `hasPrev`: Whether there are previous pages

## Error Handling

All methods include try-catch blocks and will throw errors that should be handled by the calling code. Use the ConversationAPI wrapper for built-in error handling with success/error response format.
