!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.QrCode=e()}(this,function(){"use strict";function t(t,e,n){this.ordinal_Renamed_Field=t,this.bits=e,this.name=n}function e(e){this.errorCorrectionLevel=t.forBits(e>>3&3),this.dataMask=7&e}function n(t,e){if(e||(e=t),t<1||e<1)throw"Both dimensions must be greater than 0";this.width=t,this.height=e;var n=t>>5;0!=(31&t)&&n++,this.rowSize=n,this.bits=new Array(n*e);for(var i=0;i<this.bits.length;i++)this.bits[i]=0}function i(t,e){this.count=t,this.dataCodewords=e}function r(t,e,n){this.ecCodewordsPerBlock=t,this.ecBlocks=n?[e,n]:[e]}function o(t,e,n,i,r,o){this.versionNumber=t,this.alignmentPatternCenters=e,this.ecBlocks=[n,i,r,o];for(var s=0,a=n.ecCodewordsPerBlock,h=n.getECBlocks(),w=0;w<h.length;w++){var f=h[w];s+=f.count*(f.dataCodewords+a)}this.totalCodewords=s}function s(t,e,n){this.x=t,this.y=e,this.count=1,this.estimatedModuleSize=n}function a(t,e,n,i,r,o,s){this.image=t,this.possibleCenters=[],this.startX=e,this.startY=n,this.width=i,this.height=r,this.moduleSize=o,this.crossCheckStateCount=[0,0,0],this.resultPointCallback=s}function h(t){function e(t,e){var n=t.X-e.X,i=t.Y-e.Y;return Math.sqrt(n*n+i*i)}var n,i,r,o=e(t[0],t[1]),s=e(t[1],t[2]),a=e(t[0],t[2]);if(s>=o&&s>=a?(i=t[0],n=t[1],r=t[2]):a>=s&&a>=o?(i=t[1],n=t[0],r=t[2]):(i=t[2],n=t[0],r=t[1]),function(t,e,n){var i=e.x,r=e.y;return(n.x-i)*(t.y-r)-(n.y-r)*(t.x-i)}(n,i,r)<0){var h=n;n=r,r=h}t[0]=n,t[1]=i,t[2]=r}function w(t,e,n){this.x=t,this.y=e,this.count=1,this.estimatedModuleSize=n}function f(t){this.bottomLeft=t[0],this.topLeft=t[1],this.topRight=t[2]}function u(){this.image=null,this.possibleCenters=[],this.hasSkipped=!1,this.crossCheckStateCount=[0,0,0,0,0],this.resultPointCallback=null}function l(t,e,n,i,r,o,s,a,h){this.a11=t,this.a12=i,this.a13=s,this.a21=e,this.a22=r,this.a23=a,this.a31=n,this.a32=o,this.a33=h}function d(t,e){this.bits=t,this.points=e}function c(t){this.image=t,this.resultPointCallback=null}function p(t,e){if(null==e||0==e.length)throw"System.ArgumentException";this.field=t;var n=e.length;if(n>1&&0==e[0]){for(var i=1;i<n&&0==e[i];)i++;if(i==n)this.coefficients=t.Zero.coefficients;else{this.coefficients=new Array(n-i);for(var r=0;r<this.coefficients.length;r++)this.coefficients[r]=0;for(var o=0;o<this.coefficients.length;o++)this.coefficients[o]=e[i+o]}}else this.coefficients=e}function g(t){this.expTable=new Array(256),this.logTable=new Array(256);for(var e=1,n=0;n<256;n++)this.expTable[n]=e,(e<<=1)>=256&&(e^=t);for(n=0;n<255;n++)this.logTable[this.expTable[n]]=n;var i=new Array(1);i[0]=0,this.zero=new p(this,new Array(i));var r=new Array(1);r[0]=1,this.one=new p(this,new Array(r))}function v(t){this.field=t}function m(t){var e=t.Dimension;if(e<21||1!=(3&e))throw"Error BitMatrixParser";this.bitMatrix=t,this.parsedVersion=null,this.parsedFormatInfo=null}function b(t,e){this.numDataCodewords=t,this.codewords=e}function y(t,e,n){this.blockPointer=0,this.bitPointer=7,this.dataLength=0,this.blocks=t,this.numErrorCorrectionCode=n,e<=9?this.dataLengthMode=0:e>=10&&e<=26?this.dataLengthMode=1:e>=27&&e<=40&&(this.dataLengthMode=2)}function C(){this.imagedata=null,this.width=0,this.height=0,this.qrCodeSymbol=null,this.debug=!1,this.callback=null}function M(t,e){return t>=0?t>>e:(t>>e)+(2<<~e)}t.prototype.ordinal=function(){return this.ordinal_Renamed_Field},t.forBits=function(t){if(t<0||t>=k.length)throw"ArgumentException";return k[t]};var k=[new t(1,0,"M"),new t(0,1,"L"),new t(3,2,"H"),new t(2,3,"Q")],P=[[21522,0],[20773,1],[24188,2],[23371,3],[17913,4],[16590,5],[20375,6],[19104,7],[30660,8],[29427,9],[32170,10],[30877,11],[26159,12],[25368,13],[27713,14],[26998,15],[5769,16],[5054,17],[7399,18],[6608,19],[1890,20],[597,21],[3340,22],[2107,23],[13663,24],[12392,25],[16177,26],[14854,27],[9396,28],[8579,29],[11994,30],[11245,31]],N=[0,1,1,2,1,2,2,3,1,2,2,3,2,3,3,4];e.prototype.GetHashCode=function(){return this.errorCorrectionLevel.ordinal()<<3|this.dataMask},e.prototype.Equals=function(t){var e=t;return this.errorCorrectionLevel==e.errorCorrectionLevel&&this.dataMask==e.dataMask},e.numBitsDiffering=function(t,e){return t^=e,N[15&t]+N[15&M(t,4)]+N[15&M(t,8)]+N[15&M(t,12)]+N[15&M(t,16)]+N[15&M(t,20)]+N[15&M(t,24)]+N[15&M(t,28)]},e.decodeFormatInformation=function(t){var n=e.doDecodeFormatInformation(t);return null!=n?n:e.doDecodeFormatInformation(21522^t)},e.doDecodeFormatInformation=function(t){for(var n=4294967295,i=0,r=0;r<P.length;r++){var o=P[r],s=o[0];if(s==t)return new e(o[1]);var a=this.numBitsDiffering(t,s);a<n&&(i=o[1],n=a)}return n<=3?new e(i):null},Object.defineProperty(n.prototype,"Dimension",{get:function(){if(this.width!=this.height)throw"Can't call getDimension() on a non-square matrix";return this.width}}),n.prototype.get_Renamed=function(t,e){var n=e*this.rowSize+(t>>5);return 0!=(1&M(this.bits[n],31&t))},n.prototype.set_Renamed=function(t,e){var n=e*this.rowSize+(t>>5);this.bits[n]|=1<<(31&t)},n.prototype.flip=function(t,e){var n=e*this.rowSize+(t>>5);this.bits[n]^=1<<(31&t)},n.prototype.clear=function(){for(var t=this.bits.length,e=0;e<t;e++)this.bits[e]=0},n.prototype.setRegion=function(t,e,n,i){if(e<0||t<0)throw"Left and top must be nonnegative";if(i<1||n<1)throw"Height and width must be at least 1";var r=t+n,o=e+i;if(o>this.height||r>this.width)throw"The region must fit inside the matrix";for(var s=e;s<o;s++)for(var a=s*this.rowSize,h=t;h<r;h++)this.bits[a+(h>>5)]|=1<<(31&h)},Object.defineProperty(r.prototype,"TotalECCodewords",{get:function(){return this.ecCodewordsPerBlock*this.NumBlocks}}),Object.defineProperty(r.prototype,"NumBlocks",{get:function(){for(var t=0,e=0;e<this.ecBlocks.length;e++)t+=this.ecBlocks[e].length;return t}}),r.prototype.getECBlocks=function(){return this.ecBlocks},Object.defineProperty(o.prototype,"DimensionForVersion",{get:function(){return 17+4*this.versionNumber}}),o.prototype.buildFunctionPattern=function(){var t=this.DimensionForVersion,e=new n(t);e.setRegion(0,0,9,9),e.setRegion(t-8,0,8,9),e.setRegion(0,t-8,9,8);for(var i=this.alignmentPatternCenters.length,r=0;r<i;r++)for(var o=this.alignmentPatternCenters[r]-2,s=0;s<i;s++)0==r&&(0==s||s==i-1)||r==i-1&&0==s||e.setRegion(this.alignmentPatternCenters[s]-2,o,5,5);return e.setRegion(6,9,1,t-17),e.setRegion(9,6,t-17,1),this.versionNumber>6&&(e.setRegion(t-11,0,3,6),e.setRegion(0,t-11,6,3)),e},o.prototype.getECBlocksForLevel=function(t){return this.ecBlocks[t.ordinal()]},o.VERSION_DECODE_INFO=[31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017],o.VERSIONS=[new o(1,[],new r(7,new i(1,19)),new r(10,new i(1,16)),new r(13,new i(1,13)),new r(17,new i(1,9))),new o(2,[6,18],new r(10,new i(1,34)),new r(16,new i(1,28)),new r(22,new i(1,22)),new r(28,new i(1,16))),new o(3,[6,22],new r(15,new i(1,55)),new r(26,new i(1,44)),new r(18,new i(2,17)),new r(22,new i(2,13))),new o(4,[6,26],new r(20,new i(1,80)),new r(18,new i(2,32)),new r(26,new i(2,24)),new r(16,new i(4,9))),new o(5,[6,30],new r(26,new i(1,108)),new r(24,new i(2,43)),new r(18,new i(2,15),new i(2,16)),new r(22,new i(2,11),new i(2,12))),new o(6,[6,34],new r(18,new i(2,68)),new r(16,new i(4,27)),new r(24,new i(4,19)),new r(28,new i(4,15))),new o(7,[6,22,38],new r(20,new i(2,78)),new r(18,new i(4,31)),new r(18,new i(2,14),new i(4,15)),new r(26,new i(4,13),new i(1,14))),new o(8,[6,24,42],new r(24,new i(2,97)),new r(22,new i(2,38),new i(2,39)),new r(22,new i(4,18),new i(2,19)),new r(26,new i(4,14),new i(2,15))),new o(9,[6,26,46],new r(30,new i(2,116)),new r(22,new i(3,36),new i(2,37)),new r(20,new i(4,16),new i(4,17)),new r(24,new i(4,12),new i(4,13))),new o(10,[6,28,50],new r(18,new i(2,68),new i(2,69)),new r(26,new i(4,43),new i(1,44)),new r(24,new i(6,19),new i(2,20)),new r(28,new i(6,15),new i(2,16))),new o(11,[6,30,54],new r(20,new i(4,81)),new r(30,new i(1,50),new i(4,51)),new r(28,new i(4,22),new i(4,23)),new r(24,new i(3,12),new i(8,13))),new o(12,[6,32,58],new r(24,new i(2,92),new i(2,93)),new r(22,new i(6,36),new i(2,37)),new r(26,new i(4,20),new i(6,21)),new r(28,new i(7,14),new i(4,15))),new o(13,[6,34,62],new r(26,new i(4,107)),new r(22,new i(8,37),new i(1,38)),new r(24,new i(8,20),new i(4,21)),new r(22,new i(12,11),new i(4,12))),new o(14,[6,26,46,66],new r(30,new i(3,115),new i(1,116)),new r(24,new i(4,40),new i(5,41)),new r(20,new i(11,16),new i(5,17)),new r(24,new i(11,12),new i(5,13))),new o(15,[6,26,48,70],new r(22,new i(5,87),new i(1,88)),new r(24,new i(5,41),new i(5,42)),new r(30,new i(5,24),new i(7,25)),new r(24,new i(11,12),new i(7,13))),new o(16,[6,26,50,74],new r(24,new i(5,98),new i(1,99)),new r(28,new i(7,45),new i(3,46)),new r(24,new i(15,19),new i(2,20)),new r(30,new i(3,15),new i(13,16))),new o(17,[6,30,54,78],new r(28,new i(1,107),new i(5,108)),new r(28,new i(10,46),new i(1,47)),new r(28,new i(1,22),new i(15,23)),new r(28,new i(2,14),new i(17,15))),new o(18,[6,30,56,82],new r(30,new i(5,120),new i(1,121)),new r(26,new i(9,43),new i(4,44)),new r(28,new i(17,22),new i(1,23)),new r(28,new i(2,14),new i(19,15))),new o(19,[6,30,58,86],new r(28,new i(3,113),new i(4,114)),new r(26,new i(3,44),new i(11,45)),new r(26,new i(17,21),new i(4,22)),new r(26,new i(9,13),new i(16,14))),new o(20,[6,34,62,90],new r(28,new i(3,107),new i(5,108)),new r(26,new i(3,41),new i(13,42)),new r(30,new i(15,24),new i(5,25)),new r(28,new i(15,15),new i(10,16))),new o(21,[6,28,50,72,94],new r(28,new i(4,116),new i(4,117)),new r(26,new i(17,42)),new r(28,new i(17,22),new i(6,23)),new r(30,new i(19,16),new i(6,17))),new o(22,[6,26,50,74,98],new r(28,new i(2,111),new i(7,112)),new r(28,new i(17,46)),new r(30,new i(7,24),new i(16,25)),new r(24,new i(34,13))),new o(23,[6,30,54,74,102],new r(30,new i(4,121),new i(5,122)),new r(28,new i(4,47),new i(14,48)),new r(30,new i(11,24),new i(14,25)),new r(30,new i(16,15),new i(14,16))),new o(24,[6,28,54,80,106],new r(30,new i(6,117),new i(4,118)),new r(28,new i(6,45),new i(14,46)),new r(30,new i(11,24),new i(16,25)),new r(30,new i(30,16),new i(2,17))),new o(25,[6,32,58,84,110],new r(26,new i(8,106),new i(4,107)),new r(28,new i(8,47),new i(13,48)),new r(30,new i(7,24),new i(22,25)),new r(30,new i(22,15),new i(13,16))),new o(26,[6,30,58,86,114],new r(28,new i(10,114),new i(2,115)),new r(28,new i(19,46),new i(4,47)),new r(28,new i(28,22),new i(6,23)),new r(30,new i(33,16),new i(4,17))),new o(27,[6,34,62,90,118],new r(30,new i(8,122),new i(4,123)),new r(28,new i(22,45),new i(3,46)),new r(30,new i(8,23),new i(26,24)),new r(30,new i(12,15),new i(28,16))),new o(28,[6,26,50,74,98,122],new r(30,new i(3,117),new i(10,118)),new r(28,new i(3,45),new i(23,46)),new r(30,new i(4,24),new i(31,25)),new r(30,new i(11,15),new i(31,16))),new o(29,[6,30,54,78,102,126],new r(30,new i(7,116),new i(7,117)),new r(28,new i(21,45),new i(7,46)),new r(30,new i(1,23),new i(37,24)),new r(30,new i(19,15),new i(26,16))),new o(30,[6,26,52,78,104,130],new r(30,new i(5,115),new i(10,116)),new r(28,new i(19,47),new i(10,48)),new r(30,new i(15,24),new i(25,25)),new r(30,new i(23,15),new i(25,16))),new o(31,[6,30,56,82,108,134],new r(30,new i(13,115),new i(3,116)),new r(28,new i(2,46),new i(29,47)),new r(30,new i(42,24),new i(1,25)),new r(30,new i(23,15),new i(28,16))),new o(32,[6,34,60,86,112,138],new r(30,new i(17,115)),new r(28,new i(10,46),new i(23,47)),new r(30,new i(10,24),new i(35,25)),new r(30,new i(19,15),new i(35,16))),new o(33,[6,30,58,86,114,142],new r(30,new i(17,115),new i(1,116)),new r(28,new i(14,46),new i(21,47)),new r(30,new i(29,24),new i(19,25)),new r(30,new i(11,15),new i(46,16))),new o(34,[6,34,62,90,118,146],new r(30,new i(13,115),new i(6,116)),new r(28,new i(14,46),new i(23,47)),new r(30,new i(44,24),new i(7,25)),new r(30,new i(59,16),new i(1,17))),new o(35,[6,30,54,78,102,126,150],new r(30,new i(12,121),new i(7,122)),new r(28,new i(12,47),new i(26,48)),new r(30,new i(39,24),new i(14,25)),new r(30,new i(22,15),new i(41,16))),new o(36,[6,24,50,76,102,128,154],new r(30,new i(6,121),new i(14,122)),new r(28,new i(6,47),new i(34,48)),new r(30,new i(46,24),new i(10,25)),new r(30,new i(2,15),new i(64,16))),new o(37,[6,28,54,80,106,132,158],new r(30,new i(17,122),new i(4,123)),new r(28,new i(29,46),new i(14,47)),new r(30,new i(49,24),new i(10,25)),new r(30,new i(24,15),new i(46,16))),new o(38,[6,32,58,84,110,136,162],new r(30,new i(4,122),new i(18,123)),new r(28,new i(13,46),new i(32,47)),new r(30,new i(48,24),new i(14,25)),new r(30,new i(42,15),new i(32,16))),new o(39,[6,26,54,82,110,138,166],new r(30,new i(20,117),new i(4,118)),new r(28,new i(40,47),new i(7,48)),new r(30,new i(43,24),new i(22,25)),new r(30,new i(10,15),new i(67,16))),new o(40,[6,30,58,86,114,142,170],new r(30,new i(19,118),new i(6,119)),new r(28,new i(18,47),new i(31,48)),new r(30,new i(34,24),new i(34,25)),new r(30,new i(20,15),new i(61,16)))],o.getVersionForNumber=function(t){if(t<1||t>40)throw"ArgumentException";return o.VERSIONS[t-1]},o.getProvisionalVersionForDimension=function(t){if(t%4!=1)throw"Error getProvisionalVersionForDimension";try{return o.getVersionForNumber(t-17>>2)}catch(t){throw"Error getVersionForNumber"}},o.decodeVersionInformation=function(t){for(var n=4294967295,i=0,r=0;r<o.VERSION_DECODE_INFO.length;r++){var s=o.VERSION_DECODE_INFO[r];if(s==t)return this.getVersionForNumber(r+7);var a=e.numBitsDiffering(t,s);a<n&&(i=r+7,n=a)}return n<=3?this.getVersionForNumber(i):null},Object.defineProperty(s.prototype,"X",{get:function(){return Math.floor(this.x)}}),Object.defineProperty(s.prototype,"Y",{get:function(){return Math.floor(this.y)}}),s.prototype.incrementCount=function(){this.count++},s.prototype.aboutEquals=function(t,e,n){if(Math.abs(e-this.y)<=t&&Math.abs(n-this.x)<=t){var i=Math.abs(t-this.estimatedModuleSize);return i<=1||i/this.estimatedModuleSize<=1}return!1},a.prototype.centerFromEnd=function(t,e){return e-t[2]-t[1]/2},a.prototype.foundPatternCross=function(t){for(var e=this.moduleSize,n=e/2,i=0;i<3;i++)if(Math.abs(e-t[i])>=n)return!1;return!0},a.prototype.crossCheckVertical=function(t,e,n,i){var r=this.image,o=r.height,s=this.crossCheckStateCount;s[0]=0,s[1]=0,s[2]=0;for(var a=t;a>=0&&r.data[e+a*r.width]&&s[1]<=n;)s[1]++,a--;if(a<0||s[1]>n)return NaN;for(;a>=0&&!r.data[e+a*r.width]&&s[0]<=n;)s[0]++,a--;if(s[0]>n)return NaN;for(a=t+1;a<o&&r.data[e+a*r.width]&&s[1]<=n;)s[1]++,a++;if(a==o||s[1]>n)return NaN;for(;a<o&&!r.data[e+a*r.width]&&s[2]<=n;)s[2]++,a++;if(s[2]>n)return NaN;var h=s[0]+s[1]+s[2];return 5*Math.abs(h-i)>=2*i?NaN:this.foundPatternCross(s)?this.centerFromEnd(s,a):NaN},a.prototype.handlePossibleCenter=function(t,e,n){var i=t[0]+t[1]+t[2],r=this.centerFromEnd(t,n),o=this.crossCheckVertical(e,Math.floor(r),2*t[1],i);if(!isNaN(o)){for(var a=(t[0]+t[1]+t[2])/3,h=this.possibleCenters.length,w=0;w<h;w++)if(this.possibleCenters[w].aboutEquals(a,o,r))return new s(r,o,a);var f=new s(r,o,a);this.possibleCenters.push(f),null!=this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(f)}return null},a.prototype.find=function(){for(var t=this.image,e=this.startX,n=this.height,i=e+this.width,r=this.startY+(n>>1),o=[0,0,0],s=0;s<n;s++){var a=r+(0==(1&s)?s+1>>1:-(s+1>>1));o[0]=0,o[1]=0,o[2]=0;for(var h=e;h<i&&!t.data[h+t.width*a];)h++;for(var w=0;h<i;){if(t.data[h+a*t.width])if(1==w)o[w]++;else if(2==w){if(this.foundPatternCross(o)&&null!=(f=this.handlePossibleCenter(o,a,h)))return f;o[0]=o[2],o[1]=1,o[2]=0,w=1}else o[++w]++;else 1==w&&w++,o[w]++;h++}if(this.foundPatternCross(o)){var f=this.handlePossibleCenter(o,a,i);if(null!=f)return f}}if(0!=this.possibleCenters.length)return this.possibleCenters[0];throw"Couldn't find enough alignment patterns"};var S={};S.checkAndNudgePoints=function(t,e){for(var n=t.width,i=t.height,r=!0,o=0;o<e.length&&r;o+=2){var s=Math.floor(e[o]),a=Math.floor(e[o+1]);if(s<-1||s>n||a<-1||a>i)throw"Error.checkAndNudgePoints ";r=!1,-1==s?(e[o]=0,r=!0):s==n&&(e[o]=n-1,r=!0),-1==a?(e[o+1]=0,r=!0):a==i&&(e[o+1]=i-1,r=!0)}r=!0;for(o=e.length-2;o>=0&&r;o-=2){var s=Math.floor(e[o]),a=Math.floor(e[o+1]);if(s<-1||s>n||a<-1||a>i)throw"Error.checkAndNudgePoints ";r=!1,-1==s?(e[o]=0,r=!0):s==n&&(e[o]=n-1,r=!0),-1==a?(e[o+1]=0,r=!0):a==i&&(e[o+1]=i-1,r=!0)}},S.sampleGrid3=function(t,e,i){for(var r=new n(e),o=new Array(e<<1),s=0;s<e;s++){for(var a=o.length,h=s+.5,w=0;w<a;w+=2)o[w]=.5+(w>>1),o[w+1]=h;i.transformPoints1(o),S.checkAndNudgePoints(t,o);try{for(w=0;w<a;w+=2)t.data[Math.floor(o[w])+t.width*Math.floor(o[w+1])]&&r.set_Renamed(w>>1,s)}catch(t){throw"Error.checkAndNudgePoints"}}return r};Object.defineProperty(w.prototype,"X",{get:function(){return this.x}}),Object.defineProperty(w.prototype,"Y",{get:function(){return this.y}}),w.prototype.incrementCount=function(){this.count++},w.prototype.aboutEquals=function(t,e,n){if(Math.abs(e-this.y)<=t&&Math.abs(n-this.x)<=t){var i=Math.abs(t-this.estimatedModuleSize);return i<=1||i/this.estimatedModuleSize<=1}return!1},Object.defineProperty(u.prototype,"CrossCheckStateCount",{get:function(){return this.crossCheckStateCount[0]=0,this.crossCheckStateCount[1]=0,this.crossCheckStateCount[2]=0,this.crossCheckStateCount[3]=0,this.crossCheckStateCount[4]=0,this.crossCheckStateCount}}),u.prototype.foundPatternCross=function(t){for(var e=0,n=0;n<5;n++){var i=t[n];if(0==i)return!1;e+=i}if(e<7)return!1;var r=Math.floor((e<<8)/7),o=Math.floor(r/2);return Math.abs(r-(t[0]<<8))<o&&Math.abs(r-(t[1]<<8))<o&&Math.abs(3*r-(t[2]<<8))<3*o&&Math.abs(r-(t[3]<<8))<o&&Math.abs(r-(t[4]<<8))<o},u.prototype.centerFromEnd=function(t,e){return e-t[4]-t[3]-t[2]/2},u.prototype.crossCheckVertical=function(t,e,n,i){for(var r=this.image,o=r.height,s=this.CrossCheckStateCount,a=t;a>=0&&r.data[e+a*r.width];)s[2]++,a--;if(a<0)return NaN;for(;a>=0&&!r.data[e+a*r.width]&&s[1]<=n;)s[1]++,a--;if(a<0||s[1]>n)return NaN;for(;a>=0&&r.data[e+a*r.width]&&s[0]<=n;)s[0]++,a--;if(s[0]>n)return NaN;for(a=t+1;a<o&&r.data[e+a*r.width];)s[2]++,a++;if(a==o)return NaN;for(;a<o&&!r.data[e+a*r.width]&&s[3]<n;)s[3]++,a++;if(a==o||s[3]>=n)return NaN;for(;a<o&&r.data[e+a*r.width]&&s[4]<n;)s[4]++,a++;if(s[4]>=n)return NaN;var h=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(h-i)>=2*i?NaN:this.foundPatternCross(s)?this.centerFromEnd(s,a):NaN},u.prototype.crossCheckHorizontal=function(t,e,n,i){for(var r=this.image,o=r.width,s=this.CrossCheckStateCount,a=t;a>=0&&r.data[a+e*r.width];)s[2]++,a--;if(a<0)return NaN;for(;a>=0&&!r.data[a+e*r.width]&&s[1]<=n;)s[1]++,a--;if(a<0||s[1]>n)return NaN;for(;a>=0&&r.data[a+e*r.width]&&s[0]<=n;)s[0]++,a--;if(s[0]>n)return NaN;for(a=t+1;a<o&&r.data[a+e*r.width];)s[2]++,a++;if(a==o)return NaN;for(;a<o&&!r.data[a+e*r.width]&&s[3]<n;)s[3]++,a++;if(a==o||s[3]>=n)return NaN;for(;a<o&&r.data[a+e*r.width]&&s[4]<n;)s[4]++,a++;if(s[4]>=n)return NaN;var h=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(h-i)>=i?NaN:this.foundPatternCross(s)?this.centerFromEnd(s,a):NaN},u.prototype.handlePossibleCenter=function(t,e,n){var i=t[0]+t[1]+t[2]+t[3]+t[4],r=this.centerFromEnd(t,n),o=this.crossCheckVertical(e,Math.floor(r),t[2],i);if(!isNaN(o)&&(r=this.crossCheckHorizontal(Math.floor(r),Math.floor(o),t[2],i),!isNaN(r))){for(var s=i/7,a=!1,h=this.possibleCenters.length,f=0;f<h;f++){var u=this.possibleCenters[f];if(u.aboutEquals(s,o,r)){u.incrementCount(),a=!0;break}}if(!a){var l=new w(r,o,s);this.possibleCenters.push(l),null!=this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(l)}return!0}return!1},u.prototype.selectBestPatterns=function(){var t=this.possibleCenters.length;if(t<3)throw"Couldn't find enough finder patterns:"+t+" patterns found";if(t>3){for(var e=0,n=0,i=0;i<t;i++){var r=this.possibleCenters[i].estimatedModuleSize;e+=r,n+=r*r}var o=e/t;this.possibleCenters.sort(function(t,e){var n=Math.abs(e.estimatedModuleSize-o),i=Math.abs(t.estimatedModuleSize-o);return n<i?-1:n==i?0:1});for(var s=Math.sqrt(n/t-o*o),a=Math.max(.2*o,s),i=this.possibleCenters-1;i>=0;i--){var h=this.possibleCenters[i];Math.abs(h.estimatedModuleSize-o)>a&&this.possibleCenters.splice(i,1)}}return this.possibleCenters.length>3&&this.possibleCenters.sort(function(t,e){return t.count>e.count?-1:t.count<e.count?1:0}),[this.possibleCenters[0],this.possibleCenters[1],this.possibleCenters[2]]},u.prototype.findRowSkip=function(){var t=this.possibleCenters.length;if(t<=1)return 0;for(var e=null,n=0;n<t;n++){var i=this.possibleCenters[n];if(i.count>=2){if(null!=e)return this.hasSkipped=!0,Math.floor((Math.abs(e.X-i.X)-Math.abs(e.Y-i.Y))/2);e=i}}return 0},u.prototype.haveMultiplyConfirmedCenters=function(){for(var t=0,e=0,n=this.possibleCenters.length,i=0;i<n;i++){var r=this.possibleCenters[i];r.count>=2&&(t++,e+=r.estimatedModuleSize)}if(t<3)return!1;for(var o=e/n,s=0,i=0;i<n;i++)r=this.possibleCenters[i],s+=Math.abs(r.estimatedModuleSize-o);return s<=.05*e},u.prototype.findFinderPattern=function(t){this.image=t;var e=t.height,n=t.width,i=Math.floor(3*e/228);i<3&&(i=3);for(var r=!1,o=new Array(5),s=i-1;s<e&&!r;s+=i){o[0]=0,o[1]=0,o[2]=0,o[3]=0,o[4]=0;for(var a=0,w=0;w<n;w++)if(t.data[w+s*t.width])1==(1&a)&&a++,o[a]++;else if(0==(1&a))if(4==a)if(this.foundPatternCross(o)){if(l=this.handlePossibleCenter(o,s,w))if(i=2,this.hasSkipped)r=this.haveMultiplyConfirmedCenters();else{var u=this.findRowSkip();u>o[2]&&(s+=u-o[2]-i,w=n-1)}else{do{w++}while(w<n&&!t.data[w+s*t.width]);w--}a=0,o[0]=0,o[1]=0,o[2]=0,o[3]=0,o[4]=0}else o[0]=o[2],o[1]=o[3],o[2]=o[4],o[3]=1,o[4]=0,a=3;else o[++a]++;else o[a]++;if(this.foundPatternCross(o)){var l=this.handlePossibleCenter(o,s,n);l&&(i=o[0],this.hasSkipped&&(r=this.haveMultiplyConfirmedCenters()))}}var d=this.selectBestPatterns();return h(d),new f(d)},l.prototype.transformPoints1=function(t){for(var e=t.length,n=this.a11,i=this.a12,r=this.a13,o=this.a21,s=this.a22,a=this.a23,h=this.a31,w=this.a32,f=this.a33,u=0;u<e;u+=2){var l=t[u],d=t[u+1],c=r*l+a*d+f;t[u]=(n*l+o*d+h)/c,t[u+1]=(i*l+s*d+w)/c}},l.prototype.transformPoints2=function(t,e){for(var n=t.length,i=0;i<n;i++){var r=t[i],o=e[i],s=this.a13*r+this.a23*o+this.a33;t[i]=(this.a11*r+this.a21*o+this.a31)/s,e[i]=(this.a12*r+this.a22*o+this.a32)/s}},l.prototype.buildAdjoint=function(){return new l(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},l.prototype.times=function(t){return new l(this.a11*t.a11+this.a21*t.a12+this.a31*t.a13,this.a11*t.a21+this.a21*t.a22+this.a31*t.a23,this.a11*t.a31+this.a21*t.a32+this.a31*t.a33,this.a12*t.a11+this.a22*t.a12+this.a32*t.a13,this.a12*t.a21+this.a22*t.a22+this.a32*t.a23,this.a12*t.a31+this.a22*t.a32+this.a32*t.a33,this.a13*t.a11+this.a23*t.a12+this.a33*t.a13,this.a13*t.a21+this.a23*t.a22+this.a33*t.a23,this.a13*t.a31+this.a23*t.a32+this.a33*t.a33)},l.quadrilateralToQuadrilateral=function(t,e,n,i,r,o,s,a,h,w,f,u,l,d,c,p){var g=this.quadrilateralToSquare(t,e,n,i,r,o,s,a);return this.squareToQuadrilateral(h,w,f,u,l,d,c,p).times(g)},l.squareToQuadrilateral=function(t,e,n,i,r,o,s,a){var h=a-o,w=e-i+o-a;if(0==h&&0==w)return new l(n-t,r-n,t,i-e,o-i,e,0,0,1);var f=n-r,u=s-r,d=t-n+r-s,c=i-o,p=f*h-u*c,g=(d*h-u*w)/p,v=(f*w-d*c)/p;return new l(n-t+g*n,s-t+v*s,t,i-e+g*i,a-e+v*a,e,g,v,1)},l.quadrilateralToSquare=function(t,e,n,i,r,o,s,a){return this.squareToQuadrilateral(t,e,n,i,r,o,s,a).buildAdjoint()},c.prototype.sizeOfBlackWhiteBlackRun=function(t,e,n,i){var r=Math.abs(i-e)>Math.abs(n-t);if(r){var o=t;t=e,e=o,o=n,n=i,i=o}for(var s=Math.abs(n-t),a=Math.abs(i-e),h=-s>>1,w=e<i?1:-1,f=t<n?1:-1,u=0,l=t,d=e;l!=n;l+=f){var c=r?d:l,p=r?l:d;if(1==u?this.image.data[c+p*this.image.width]&&u++:this.image.data[c+p*this.image.width]||u++,3==u){var g=l-t,v=d-e;return Math.sqrt(g*g+v*v)}if((h+=a)>0){if(d==i)break;d+=w,h-=s}}var m=n-t,b=i-e;return Math.sqrt(m*m+b*b)},c.prototype.sizeOfBlackWhiteBlackRunBothWays=function(t,e,n,i){var r=this.sizeOfBlackWhiteBlackRun(t,e,n,i),o=1,s=t-(n-t);s<0?(o=t/(t-s),s=0):s>=this.image.width&&(o=(this.image.width-1-t)/(s-t),s=this.image.width-1);var a=Math.floor(e-(i-e)*o);return o=1,a<0?(o=e/(e-a),a=0):a>=this.image.height&&(o=(this.image.height-1-e)/(a-e),a=this.image.height-1),s=Math.floor(t+(s-t)*o),(r+=this.sizeOfBlackWhiteBlackRun(t,e,s,a))-1},c.prototype.calculateModuleSizeOneWay=function(t,e){var n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.X),Math.floor(t.Y),Math.floor(e.X),Math.floor(e.Y)),i=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.X),Math.floor(e.Y),Math.floor(t.X),Math.floor(t.Y));return isNaN(n)?i/7:isNaN(i)?n/7:(n+i)/14},c.prototype.calculateModuleSize=function(t,e,n){return(this.calculateModuleSizeOneWay(t,e)+this.calculateModuleSizeOneWay(t,n))/2},c.prototype.distance=function(t,e){var n=t.X-e.X,i=t.Y-e.Y;return Math.sqrt(n*n+i*i)},c.prototype.computeDimension=function(t,e,n,i){var r=7+(Math.round(this.distance(t,e)/i)+Math.round(this.distance(t,n)/i)>>1);switch(3&r){case 0:r++;break;case 2:r--;break;case 3:throw"Error"}return r},c.prototype.findAlignmentInRegion=function(t,e,n,i){var r=Math.floor(i*t),o=Math.max(0,e-r),s=Math.min(this.image.width-1,e+r);if(s-o<3*t)throw"Error";var h=Math.max(0,n-r),w=Math.min(this.image.height-1,n+r);return new a(this.image,o,h,s-o,w-h,t,this.resultPointCallback).find()},c.prototype.createTransform=function(t,e,n,i,r){var o,s,a,h,w=r-3.5;return null!=i?(o=i.X,s=i.Y,a=h=w-3):(o=e.X-t.X+n.X,s=e.Y-t.Y+n.Y,a=h=w),l.quadrilateralToQuadrilateral(3.5,3.5,w,3.5,a,h,3.5,w,t.X,t.Y,e.X,e.Y,o,s,n.X,n.Y)},c.prototype.sampleGrid=function(t,e,n){return S.sampleGrid3(t,n,e)},c.prototype.processFinderPatternInfo=function(t){var e=t.topLeft,n=t.topRight,i=t.bottomLeft,r=this.calculateModuleSize(e,n,i);if(r<1)throw"Error";var s=this.computeDimension(e,n,i,r),a=o.getProvisionalVersionForDimension(s),h=a.DimensionForVersion-7,w=null;if(a.alignmentPatternCenters.length>0)for(var f=n.X-e.X+i.X,u=n.Y-e.Y+i.Y,l=1-3/h,c=Math.floor(e.X+l*(f-e.X)),p=Math.floor(e.Y+l*(u-e.Y)),g=4;g<=16;g<<=1){w=this.findAlignmentInRegion(r,c,p,g);break}var v,m=this.createTransform(e,n,i,w,s),b=this.sampleGrid(this.image,m,s);return v=null==w?[i,e,n]:[i,e,n,w],new d(b,v)},c.prototype.detect=function(){var t=(new u).findFinderPattern(this.image);return this.processFinderPatternInfo(t)},Object.defineProperty(p.prototype,"Zero",{get:function(){return 0==this.coefficients[0]}}),Object.defineProperty(p.prototype,"Degree",{get:function(){return this.coefficients.length-1}}),p.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},p.prototype.evaluateAt=function(t){if(0==t)return this.getCoefficient(0);var e=this.coefficients.length;if(1==t){for(var n=0,i=0;i<e;i++)n=this.field.addOrSubtract(n,this.coefficients[i]);return n}for(var r=this.coefficients[0],i=1;i<e;i++)r=this.field.addOrSubtract(this.field.multiply(t,r),this.coefficients[i]);return r},p.prototype.addOrSubtract=function(t){if(this.field!=t.field)throw"GF256Polys do not have same GF256 field";if(this.Zero)return t;if(t.Zero)return this;var e=this.coefficients,n=t.coefficients;if(e.length>n.length){var i=e;e=n,n=i}for(var r=new Array(n.length),o=n.length-e.length,s=0;s<o;s++)r[s]=n[s];for(var a=o;a<n.length;a++)r[a]=this.field.addOrSubtract(e[a-o],n[a]);return new p(this.field,r)},p.prototype.multiply1=function(t){if(this.field!=t.field)throw"GF256Polys do not have same GF256 field";if(this.Zero||t.Zero)return this.field.Zero;for(var e=this.coefficients,n=e.length,i=t.coefficients,r=i.length,o=new Array(n+r-1),s=0;s<n;s++)for(var a=e[s],h=0;h<r;h++)o[s+h]=this.field.addOrSubtract(o[s+h],this.field.multiply(a,i[h]));return new p(this.field,o)},p.prototype.multiply2=function(t){if(0==t)return this.field.Zero;if(1==t)return this;for(var e=this.coefficients.length,n=new Array(e),i=0;i<e;i++)n[i]=this.field.multiply(this.coefficients[i],t);return new p(this.field,n)},p.prototype.multiplyByMonomial=function(t,e){if(t<0)throw"System.ArgumentException";if(0==e)return this.field.Zero;for(var n=this.coefficients.length,i=new Array(n+t),r=0;r<i.length;r++)i[r]=0;for(r=0;r<n;r++)i[r]=this.field.multiply(this.coefficients[r],e);return new p(this.field,i)},p.prototype.divide=function(t){if(this.field!=t.field)throw"GF256Polys do not have same GF256 field";if(t.Zero)throw"Divide by 0";for(var e=this.field.Zero,n=this,i=t.getCoefficient(t.Degree),r=this.field.inverse(i);n.Degree>=t.Degree&&!n.Zero;){var o=n.Degree-t.Degree,s=this.field.multiply(n.getCoefficient(n.Degree),r),a=t.multiplyByMonomial(o,s),h=this.field.buildMonomial(o,s);e=e.addOrSubtract(h),n=n.addOrSubtract(a)}return[e,n]},Object.defineProperty(g.prototype,"Zero",{get:function(){return this.zero}}),Object.defineProperty(g.prototype,"One",{get:function(){return this.one}}),g.prototype.buildMonomial=function(t,e){if(t<0)throw"System.ArgumentException";if(0==e)return this.zero;for(var n=new Array(t+1),i=0;i<n.length;i++)n[i]=0;return n[0]=e,new p(this,n)},g.prototype.exp=function(t){return this.expTable[t]},g.prototype.log=function(t){if(0==t)throw"System.ArgumentException";return this.logTable[t]},g.prototype.inverse=function(t){if(0==t)throw"System.ArithmeticException";return this.expTable[255-this.logTable[t]]},g.prototype.addOrSubtract=function(t,e){return t^e},g.prototype.multiply=function(t,e){return 0==t||0==e?0:1==t?e:1==e?t:this.expTable[(this.logTable[t]+this.logTable[e])%255]},g.QR_CODE_FIELD=new g(285),g.DATA_MATRIX_FIELD=new g(301),v.prototype.decode=function(t,e){for(var n=new p(this.field,t),i=new Array(e),r=0;r<i.length;r++)i[r]=0;for(var o=!0,r=0;r<e;r++){var s=n.evaluateAt(this.field.exp(r));i[i.length-1-r]=s,0!=s&&(o=!1)}if(!o)for(var a=new p(this.field,i),h=this.runEuclideanAlgorithm(this.field.buildMonomial(e,1),a,e),w=h[0],f=h[1],u=this.findErrorLocations(w),l=this.findErrorMagnitudes(f,u,!1),r=0;r<u.length;r++){var d=t.length-1-this.field.log(u[r]);if(d<0)throw"ReedSolomonException Bad error location";t[d]=g.prototype.addOrSubtract(t[d],l[r])}},v.prototype.runEuclideanAlgorithm=function(t,e,n){if(t.Degree<e.Degree){var i=t;t=e,e=i}for(var r=t,o=e,s=this.field.One,a=this.field.Zero,h=this.field.Zero,w=this.field.One;o.Degree>=Math.floor(n/2);){var f=r,u=s,l=h;if(r=o,s=a,h=w,r.Zero)throw"r_{i-1} was zero";o=f;for(var d=this.field.Zero,c=r.getCoefficient(r.Degree),p=this.field.inverse(c);o.Degree>=r.Degree&&!o.Zero;){var g=o.Degree-r.Degree,v=this.field.multiply(o.getCoefficient(o.Degree),p);d=d.addOrSubtract(this.field.buildMonomial(g,v)),o=o.addOrSubtract(r.multiplyByMonomial(g,v))}a=d.multiply1(s).addOrSubtract(u),w=d.multiply1(h).addOrSubtract(l)}var m=w.getCoefficient(0);if(0==m)throw"ReedSolomonException sigmaTilde(0) was zero";var b=this.field.inverse(m);return[w.multiply2(b),o.multiply2(b)]},v.prototype.findErrorLocations=function(t){var e=t.Degree;if(1==e)return new Array(t.getCoefficient(1));for(var n=new Array(e),i=0,r=1;r<256&&i<e;r++)0==t.evaluateAt(r)&&(n[i]=this.field.inverse(r),i++);if(i!=e)throw"Error locator degree does not match number of roots";return n},v.prototype.findErrorMagnitudes=function(t,e,n){for(var i=e.length,r=new Array(i),o=0;o<i;o++){for(var s=this.field.inverse(e[o]),a=1,h=0;h<i;h++)o!=h&&(a=this.field.multiply(a,g.prototype.addOrSubtract(1,this.field.multiply(e[h],s))));r[o]=this.field.multiply(t.evaluateAt(s),this.field.inverse(a)),n&&(r[o]=this.field.multiply(r[o],s))}return r};var B={};B.forReference=function(t){if(t<0||t>7)throw"System.ArgumentException";return B.DATA_MASKS[t]},B.DATA_MASKS=[new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){return 0==(t+e&1)}},new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){return 0==(1&t)}},new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){return e%3==0}},new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){return(t+e)%3==0}},new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){return 0==(M(t,1)+e/3&1)}},new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){var n=t*e;return(1&n)+n%3==0}},new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){var n=t*e;return 0==((1&n)+n%3&1)}},new function(){this.unmaskBitMatrix=function(t,e){for(var n=0;n<e;n++)for(var i=0;i<e;i++)this.isMasked(n,i)&&t.flip(i,n)},this.isMasked=function(t,e){return 0==((t+e&1)+t*e%3&1)}}],m.prototype.copyBit=function(t,e,n){return this.bitMatrix.get_Renamed(t,e)?n<<1|1:n<<1},m.prototype.readFormatInformation=function(){if(null!=this.parsedFormatInfo)return this.parsedFormatInfo;for(var t=0,n=0;n<6;n++)t=this.copyBit(n,8,t);t=this.copyBit(7,8,t),t=this.copyBit(8,8,t),t=this.copyBit(8,7,t);for(o=5;o>=0;o--)t=this.copyBit(8,o,t);if(this.parsedFormatInfo=e.decodeFormatInformation(t),null!=this.parsedFormatInfo)return this.parsedFormatInfo;var i=this.bitMatrix.Dimension;t=0;for(var r=i-8,n=i-1;n>=r;n--)t=this.copyBit(n,8,t);for(var o=i-7;o<i;o++)t=this.copyBit(8,o,t);if(this.parsedFormatInfo=e.decodeFormatInformation(t),null!=this.parsedFormatInfo)return this.parsedFormatInfo;throw"Error readFormatInformation"},m.prototype.readVersion=function(){if(null!=this.parsedVersion)return this.parsedVersion;var t=this.bitMatrix.Dimension,e=t-17>>2;if(e<=6)return o.getVersionForNumber(e);for(var n=0,i=t-11,r=5;r>=0;r--)for(s=t-9;s>=i;s--)n=this.copyBit(s,r,n);if(this.parsedVersion=o.decodeVersionInformation(n),null!=this.parsedVersion&&this.parsedVersion.DimensionForVersion==t)return this.parsedVersion;n=0;for(var s=5;s>=0;s--)for(r=t-9;r>=i;r--)n=this.copyBit(s,r,n);if(this.parsedVersion=o.decodeVersionInformation(n),null!=this.parsedVersion&&this.parsedVersion.DimensionForVersion==t)return this.parsedVersion;throw"Error readVersion"},m.prototype.readCodewords=function(){var t=this.readFormatInformation(),e=this.readVersion(),n=B.forReference(t.dataMask),i=this.bitMatrix.Dimension;n.unmaskBitMatrix(this.bitMatrix,i);for(var r=e.buildFunctionPattern(),o=!0,s=new Array(e.totalCodewords),a=0,h=0,w=0,f=i-1;f>0;f-=2){6==f&&f--;for(var u=0;u<i;u++)for(var l=o?i-1-u:u,d=0;d<2;d++)r.get_Renamed(f-d,l)||(w++,h<<=1,this.bitMatrix.get_Renamed(f-d,l)&&(h|=1),8==w&&(s[a++]=h,w=0,h=0));o^=!0}if(a!=e.totalCodewords)throw"Error readCodewords";return s},b.getDataBlocks=function(t,e,n){if(t.length!=e.totalCodewords)throw"ArgumentException";for(var i=e.getECBlocksForLevel(n),r=0,o=i.getECBlocks(),s=0;s<o.length;s++)r+=o[s].count;for(var a=new Array(r),h=0,w=0;w<o.length;w++)for(var f=o[w],s=0;s<f.count;s++){var u=f.dataCodewords,l=i.ecCodewordsPerBlock+u;a[h++]=new b(u,new Array(l))}for(var d=a[0].codewords.length,c=a.length-1;c>=0&&a[c].codewords.length!=d;)c--;c++;for(var p=d-i.ecCodewordsPerBlock,g=0,s=0;s<p;s++)for(w=0;w<h;w++)a[w].codewords[s]=t[g++];for(w=c;w<h;w++)a[w].codewords[p]=t[g++];for(var v=a[0].codewords.length,s=p;s<v;s++)for(w=0;w<h;w++){var m=w<c?s:s+1;a[w].codewords[m]=t[g++]}return a},y.prototype.getNextBits=function(t){var e=0;if(t<this.bitPointer+1){for(var n=0,i=0;i<t;i++)n+=1<<i;return n<<=this.bitPointer-t+1,e=(this.blocks[this.blockPointer]&n)>>this.bitPointer-t+1,this.bitPointer-=t,e}if(t<this.bitPointer+1+8){for(var r=0,i=0;i<this.bitPointer+1;i++)r+=1<<i;return e=(this.blocks[this.blockPointer]&r)<<t-(this.bitPointer+1),this.blockPointer++,e+=this.blocks[this.blockPointer]>>8-(t-(this.bitPointer+1)),this.bitPointer=this.bitPointer-t%8,this.bitPointer<0&&(this.bitPointer=8+this.bitPointer),e}if(t<this.bitPointer+1+16){for(var r=0,o=0,i=0;i<this.bitPointer+1;i++)r+=1<<i;var s=(this.blocks[this.blockPointer]&r)<<t-(this.bitPointer+1);this.blockPointer++;var a=this.blocks[this.blockPointer]<<t-(this.bitPointer+1+8);this.blockPointer++;for(i=0;i<t-(this.bitPointer+1+8);i++)o+=1<<i;return o<<=8-(t-(this.bitPointer+1+8)),e=s+a+((this.blocks[this.blockPointer]&o)>>8-(t-(this.bitPointer+1+8))),this.bitPointer=this.bitPointer-(t-8)%8,this.bitPointer<0&&(this.bitPointer=8+this.bitPointer),e}return 0},y.prototype.NextMode=function(){return this.blockPointer>this.blocks.length-this.numErrorCorrectionCode-2?0:this.getNextBits(4)},y.prototype.getDataLength=function(t){for(var e=0;;){if(t>>e==1)break;e++}return this.getNextBits(E.sizeOfDataLengthInfo[this.dataLengthMode][e])},y.prototype.getRomanAndFigureString=function(t){var e=t,n=0,i="",r=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];do{if(e>1){var o=(n=this.getNextBits(11))%45;i+=r[Math.floor(n/45)],i+=r[o],e-=2}else 1==e&&(i+=r[n=this.getNextBits(6)],e-=1)}while(e>0);return i},y.prototype.getFigureString=function(t){var e=t,n=0,i="";do{e>=3?((n=this.getNextBits(10))<100&&(i+="0"),n<10&&(i+="0"),e-=3):2==e?((n=this.getNextBits(7))<10&&(i+="0"),e-=2):1==e&&(n=this.getNextBits(4),e-=1),i+=n}while(e>0);return i},y.prototype.get8bitByteArray=function(t){var e=t,n=0,i=[];do{n=this.getNextBits(8),i.push(n),e--}while(e>0);return i},y.prototype.getKanjiString=function(t){var e=t,n=0,i="";do{var r=((n=this.getNextBits(13))/192<<8)+n%192,o=0;o=r+33088<=40956?r+33088:r+49472,i+=String.fromCharCode(o),e--}while(e>0);return i},Object.defineProperty(y.prototype,"DataByte",{get:function(){for(var t=[];;){var e=this.NextMode();if(0==e){if(t.length>0)break;throw"Empty data block"}if(1!=e&&2!=e&&4!=e&&8!=e&&7!=e)throw"Invalid mode: "+e+" in (block:"+this.blockPointer+" bit:"+this.bitPointer+")";var n=this.getDataLength(e);if(n<1)throw"Invalid data length: "+n;switch(e){case 1:for(var i=this.getFigureString(n),r=new Array(i.length),o=0;o<i.length;o++)r[o]=i.charCodeAt(o);t.push(r);break;case 2:for(var i=this.getRomanAndFigureString(n),r=new Array(i.length),o=0;o<i.length;o++)r[o]=i.charCodeAt(o);t.push(r);break;case 4:var s=this.get8bitByteArray(n);t.push(s);break;case 8:i=this.getKanjiString(n);t.push(i)}}return t}});var A={};A.rsDecoder=new v(g.QR_CODE_FIELD),A.correctErrors=function(t,e){for(var n=t.length,i=new Array(n),r=0;r<n;r++)i[r]=255&t[r];var o=t.length-e;try{A.rsDecoder.decode(i,o)}catch(t){throw t}for(r=0;r<e;r++)t[r]=i[r]},A.decode=function(t){for(var e=new m(t),n=e.readVersion(),i=e.readFormatInformation().errorCorrectionLevel,r=e.readCodewords(),o=b.getDataBlocks(r,n,i),s=0,a=0;a<o.length;a++)s+=o[a].numDataCodewords;for(var h=new Array(s),w=0,f=0;f<o.length;f++){var u=o[f],l=u.codewords,d=u.numDataCodewords;A.correctErrors(l,d);for(a=0;a<d;a++)h[w++]=l[a]}return new y(h,n.versionNumber,i.bits)};var E={};return E.sizeOfDataLengthInfo=[[10,9,8,8],[12,11,16,10],[14,13,16,12]],C.prototype.decode=function(t,e){var n=function(){try{this.error=void 0,this.result=this.process(this.imagedata)}catch(t){this.error=t,this.result=void 0}return null!=this.callback&&this.callback(this.error,this.result),this.result}.bind(this);if(void 0!=t&&void 0!=t.width)this.width=t.width,this.height=t.height,this.imagedata={data:e||t.data},this.imagedata.width=t.width,this.imagedata.height=t.height,n();else{if("undefined"==typeof Image)throw new Error("This source format is not supported in your environment, you need to pass an image buffer with width and height (see https://github.com/edi9999/jsqrcode/blob/master/test/qrcode.js)");var i=new Image;i.crossOrigin="Anonymous",i.onload=function(){var t=document.createElement("canvas"),e=t.getContext("2d"),r=document.getElementById("out-canvas");if(null!=r){var o=r.getContext("2d");o.clearRect(0,0,320,240),o.drawImage(i,0,0,320,240)}t.width=i.width,t.height=i.height,e.drawImage(i,0,0),this.width=i.width,this.height=i.height;try{this.imagedata=e.getImageData(0,0,i.width,i.height)}catch(t){if(this.result="Cross domain image reading not supported in your browser! Save it to your computer then drag and drop the file!",null!=this.callback)return this.callback(null,this.result)}n()}.bind(this),i.src=t}},C.prototype.decode_utf8=function(t){return decodeURIComponent(escape(t))},C.prototype.process=function(t){for(var e=(new Date).getTime(),n=new c(this.grayScaleToBitmap(this.grayscale(t))).detect(),i=A.decode(n.bits).DataByte,r="",o=0;o<i.length;o++)for(var s=0;s<i[o].length;s++)r+=String.fromCharCode(i[o][s]);var a=(new Date).getTime()-e;return this.debug&&console.log("QR Code processing time (ms): "+a),{result:this.decode_utf8(r),points:n.points}},C.prototype.getPixel=function(t,e,n){if(t.width<e)throw"point error";if(t.height<n)throw"point error";var i=4*e+n*t.width*4;return(33*t.data[i]+34*t.data[i+1]+33*t.data[i+2])/100},C.prototype.binarize=function(t){for(var e=new Array(this.width*this.height),n=0;n<this.height;n++)for(var i=0;i<this.width;i++){var r=this.getPixel(i,n);e[i+n*this.width]=r<=t}return e},C.prototype.getMiddleBrightnessPerArea=function(t){for(var e=Math.floor(t.width/4),n=Math.floor(t.height/4),i=new Array(4),r=0;r<4;r++){i[r]=new Array(4);for(var o=0;o<4;o++)i[r][o]=[0,0]}for(u=0;u<4;u++)for(l=0;l<4;l++){i[l][u][0]=255;for(var s=0;s<n;s++)for(var a=0;a<e;a++){var h=t.data[e*l+a+(n*u+s)*t.width];h<i[l][u][0]&&(i[l][u][0]=h),h>i[l][u][1]&&(i[l][u][1]=h)}}for(var w=new Array(4),f=0;f<4;f++)w[f]=new Array(4);for(var u=0;u<4;u++)for(var l=0;l<4;l++)w[l][u]=Math.floor((i[l][u][0]+i[l][u][1])/2);return w},C.prototype.grayScaleToBitmap=function(t){for(var e=this.getMiddleBrightnessPerArea(t),n=e.length,i=Math.floor(t.width/n),r=Math.floor(t.height/n),o=0;o<n;o++)for(var s=0;s<n;s++)for(var a=0;a<r;a++)for(var h=0;h<i;h++)t.data[i*s+h+(r*o+a)*t.width]=t.data[i*s+h+(r*o+a)*t.width]<e[s][o];return t},C.prototype.grayscale=function(t){for(var e=new Array(t.width*t.height),n=0;n<t.height;n++)for(var i=0;i<t.width;i++){var r=this.getPixel(t,i,n);e[i+n*t.width]=r}return{height:t.height,width:t.width,data:e}},C});