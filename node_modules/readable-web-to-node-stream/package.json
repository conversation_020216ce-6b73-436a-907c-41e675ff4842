{"name": "readable-web-to-node-stream", "version": "3.0.4", "description": "Converts a Web-API readable-stream into a Node readable-stream.", "type": "commonjs", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/index.js", "lib/index.d.ts"], "engines": {"node": ">=8"}, "scripts": {"clean": "del-cli 'lib/**/*.js' 'lib/**/*.js.map' 'lib/**/*.d.ts' 'coverage'", "compile-lib": "tsc -p lib/tsconfig.json", "compile-test": "tsc -p lib/tsconfig.spec.json", "prepublishOnly": "yarn run build", "build": "yarn run compile-lib && npm run compile-test", "lint-ts": "biome check", "lint": "yarn run biome check", "test": "karma start --single-run", "karma-headless": "karma start --single-run --reporters coverage-istanbul,spec,progress", "karma": "karma start", "karma-firefox": "karma start --browsers Firefox", "karma-once": "karma start --browsers Chrome --single-run", "post-coveralls": "coveralls < coverage/lcov.info", "post-codacy": " codacy-coverage < coverage/lcov.info"}, "keywords": ["stream.readable", "web", "node", "browser", "stream", "covert", "coverter", "readable", "readablestream"], "repository": "https://github.com/Borewit/readable-web-to-node-stream.git", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Borewit"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}, "license": "MIT", "bugs": {"url": "https://github.com/Borewit/readable-web-to-node-stream/issues"}, "dependencies": {"readable-stream": "^4.7.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/jasmine": "^5.1.6", "@types/node": "^22.13.4", "@types/readable-stream": "^4.0.18", "coveralls": "^3.1.1", "del-cli": "^6.0.0", "istanbul-instrumenter-loader": "^3.0.1", "jasmine-core": "^5.6.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "karma-safari-launcher": "^1.0.0", "karma-spec-reporter": "^0.0.36", "karma-webpack": "^5.0.1", "music-metadata-browser": "^2.5.11", "ts-loader": "^9.5.2", "typescript": "^5.7.3", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "packageManager": "yarn@4.6.0"}