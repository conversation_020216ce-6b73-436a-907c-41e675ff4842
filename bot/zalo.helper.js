import { Zalo, ThreadType, Reactions } from "zca-js";
import qrcodeTerminal from "qrcode-terminal";
import fs from "fs";
function groupItems(messages) {
  const grouped = {};

  // Group items by groupLayoutId or msgId for items without extraData
  messages.forEach((item) => {
    // Ensure item has data
    if (!item.data) return;

    let parsedExtraData, parsedParams, groupLayoutId;

    // Try parsing params if available
    if (item.data.content?.params) {
      try {
        parsedParams = JSON.parse(item.data.content.params);
      } catch (e) {
        console.error("Error parsing params JSON:", e);
      }
    }

    // Check for extraData
    if (item.data.extraData) {
      try {
        parsedExtraData = JSON.parse(item.data.extraData);
        groupLayoutId = parsedExtraData?.groupMediaMsg?.groupLayoutId;
      } catch (e) {
        console.error("Error parsing extraData JSON:", e);
      }
    }

    // Use msgId as groupLayoutId for items without extraData or groupLayoutId
    groupLayoutId = groupLayoutId || item.data.msgId;

    if (!groupLayoutId) return;

    if (!grouped[groupLayoutId]) {
      grouped[groupLayoutId] = [];
    }

    // Add parsed params to item for sorting
    grouped[groupLayoutId].push({
      ...item,
      parsedParams: parsedParams || {},
      groupLayoutId,
    });
  });

  // Sort each group by id_in_group and return as array
  return Object.values(grouped).map((group) => ({
    groupLayoutId: group[0].groupLayoutId,
    items: group.sort((a, b) => {
      const idA = a.parsedParams?.id_in_group || 0;
      const idB = b.parsedParams?.id_in_group || 0;
      return idA - idB;
    }),
  }));
}
export default class ZaloHelper {
  constructor() {
    this.zaloClient = new Zalo({
      selfListen: false, // mặc định false, lắng nghe sự kiện của bản thân
      checkUpdate: true, // mặc định true, kiểm tra update
      logging: true, // mặc định true, bật/tắt log mặc định của thư viện
    });
    this.dataCookie = {};
    this.messages = [];
    this.debounceTimeout = null;
  }

  async requestQR() {
    try {
      const self = this;
      if (fs.existsSync("cookie.json")) {
        const cookie = JSON.parse(fs.readFileSync("cookie.json", "utf8"));
        this.api = await this.zaloClient.login(cookie);
      } else {
        this.api = await this.zaloClient.loginQR(undefined, (qrPath) => {
          if (qrPath.data.token) {
            qrcodeTerminal.generate(qrPath.data.token, { small: true });
          }
          self.dataCookie["cookie"] = qrPath.data.cookie;
          self.dataCookie["imei"] = qrPath.data.imei;
          self.dataCookie["userAgent"] = qrPath.data.userAgent;
          if (
            self.dataCookie["cookie"] &&
            self.dataCookie["imei"] &&
            self.dataCookie["userAgent"]
          ) {
            fs.writeFileSync("cookie.json", JSON.stringify(self.dataCookie));
            console.log("Login success");
            self.keepAlive();
          }
        });
      }

      this.api.listener.on("message", (message) => {
        this.messages.push(message);

        // Clear existing timeout if a new message arrives
        if (this.debounceTimeout) {
          clearTimeout(this.debounceTimeout);
        }

        // Set a new timeout to process messages after 3 seconds of inactivity
        this.debounceTimeout = setTimeout(() => {
          this.onReceivedMessages();
        }, 3000);

        // if (message.type === ThreadType.User) {
        //   console.log("Received message from user");
        // } else {
        //   console.log("Received message from group");
        // }
        // const { msgType, content } = message.data;
        // if (msgType === "chat.photo") {
        //   const href = content.href;

        //   console.log("Received image message", href);
        // }
        // await this.api.addReaction(Reactions.HEART, message);
        // this.onReceivedMessage(message);
      });
      this.api.listener.start();
    } catch (error) {
      console.error("Error requesting QR:", error);
      throw error;
    }
  }
  keepAlive() {
    const interval = setInterval(async () => {
      try {
        if (this.api) {
          await this.api.keepAlive();
        } else {
          clearInterval(interval);
        }
        console.log("Keep alive");
      } catch (error) {
        console.error("Error keep alive:", error);
      }
    }, 30000);
  }

  async onReceivedMessages() {
    console.log(this.messages);
    const grouped = groupItems(this.messages);
    console.log(grouped);
  }
  async getFriendRquest() {
    try {
      const friendRequest = await this.api.getFriendRequest();
      return friendRequest;
    } catch (error) {
      console.error("Error getting friend request:", error);
      throw error;
    }
  }
  async sendFriendRequest(userId) {
    try {
      const friendRequest = await this.api.sendFriendRequest(
        "Hello from Bot",
        userId
      );
      return friendRequest;
    } catch (error) {
      console.error("Error getting friend request:", error);
      throw error;
    }
  }
  async findUserByPhone(phoneNumber) {
    try {
      const user = await this.api.findUser(phoneNumber);
      return user;
    } catch (error) {
      console.error("Error finding user:", error);
      throw error;
    }
  }
  async acceptFriendRequest(userId) {
    try {
      const friendRequest = await this.api.acceptFriendRequest(userId);
      return friendRequest;
    } catch (error) {
      console.error("Error accepting friend request:", error);
      throw error;
    }
  }
}
