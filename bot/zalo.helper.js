import { Zalo, ThreadType, Reactions } from "zca-js";
import qrcodeTerminal from "qrcode-terminal";
import fs from "fs";
import { PrismaClient } from "@prisma/client";
function groupItems(messages) {
  const grouped = {};

  // Group items by groupLayoutId or msgId for items without extraData
  messages.forEach((item) => {
    // Ensure item has data
    if (!item.data) return;

    let parsedExtraData, parsedParams, groupLayoutId;

    // Try parsing params if available
    if (item.data.content?.params) {
      try {
        parsedParams = JSON.parse(item.data.content.params);
      } catch (e) {
        console.error("Error parsing params JSON:", e);
      }
    }

    // Check for extraData
    if (item.data.extraData) {
      try {
        parsedExtraData = JSON.parse(item.data.extraData);
        groupLayoutId = parsedExtraData?.groupMediaMsg?.groupLayoutId;
      } catch (e) {
        console.error("Error parsing extraData JSON:", e);
      }
    }

    // Use msgId as groupLayoutId for items without extraData or groupLayoutId
    groupLayoutId = groupLayoutId || item.data.msgId;

    if (!groupLayoutId) return;

    if (!grouped[groupLayoutId]) {
      grouped[groupLayoutId] = [];
    }

    // Add parsed params to item for sorting
    grouped[groupLayoutId].push({
      ...item,
      parsedParams: parsedParams || {},
      groupLayoutId,
    });
  });

  // Sort each group by id_in_group and return as array
  return Object.values(grouped).map((group) => ({
    groupLayoutId: group[0].groupLayoutId,
    items: group.sort((a, b) => {
      const idA = a.parsedParams?.id_in_group || 0;
      const idB = b.parsedParams?.id_in_group || 0;
      return idA - idB;
    }),
  }));
}
export default class ZaloHelper {
  constructor() {
    this.zaloClient = new Zalo({
      selfListen: false, // mặc định false, lắng nghe sự kiện của bản thân
      checkUpdate: true, // mặc định true, kiểm tra update
      logging: true, // mặc định true, bật/tắt log mặc định của thư viện
    });
    this.dataCookie = {};
    this.messages = [];
    this.debounceTimeout = null;
    this.prisma = new PrismaClient();
  }

  async requestQR() {
    try {
      const self = this;
      if (fs.existsSync("cookie.json")) {
        const cookie = JSON.parse(fs.readFileSync("cookie.json", "utf8"));
        this.api = await this.zaloClient.login(cookie);
      } else {
        this.api = await this.zaloClient.loginQR(undefined, (qrPath) => {
          if (qrPath.data.token) {
            qrcodeTerminal.generate(qrPath.data.token, { small: true });
          }
          self.dataCookie["cookie"] = qrPath.data.cookie;
          self.dataCookie["imei"] = qrPath.data.imei;
          self.dataCookie["userAgent"] = qrPath.data.userAgent;
          if (
            self.dataCookie["cookie"] &&
            self.dataCookie["imei"] &&
            self.dataCookie["userAgent"]
          ) {
            fs.writeFileSync("cookie.json", JSON.stringify(self.dataCookie));
            console.log("Login success");
            self.keepAlive();
          }
        });
      }

      this.api.listener.on("message", (message) => {
        this.messages.push(message);

        // Clear existing timeout if a new message arrives
        if (this.debounceTimeout) {
          clearTimeout(this.debounceTimeout);
        }

        // Set a new timeout to process messages after 3 seconds of inactivity
        this.debounceTimeout = setTimeout(() => {
          this.onReceivedMessages();
        }, 3000);

        // if (message.type === ThreadType.User) {
        //   console.log("Received message from user");
        // } else {
        //   console.log("Received message from group");
        // }
        // const { msgType, content } = message.data;
        // if (msgType === "chat.photo") {
        //   const href = content.href;

        //   console.log("Received image message", href);
        // }
        // await this.api.addReaction(Reactions.HEART, message);
        // this.onReceivedMessage(message);
      });
      this.api.listener.start();
    } catch (error) {
      console.error("Error requesting QR:", error);
      throw error;
    }
  }
  keepAlive() {
    const interval = setInterval(async () => {
      try {
        if (this.api) {
          await this.api.keepAlive();
        } else {
          clearInterval(interval);
        }
        console.log("Keep alive");
      } catch (error) {
        console.error("Error keep alive:", error);
      }
    }, 30000);
  }

  async onReceivedMessages() {
    console.log("Processing messages:", this.messages.length);
    const grouped = groupItems(this.messages);
    console.log("Grouped messages:", grouped.length);

    try {
      // Process each group of messages
      for (const group of grouped) {
        for (const messageItem of group.items) {
          const { data } = messageItem;
          if (!data) continue;

          const uidFrom = data.uidFrom;
          const uidTo = data.uidTo || data.idTo;

          if (!uidFrom || !uidTo) {
            console.log("Missing uidFrom or uidTo, skipping message");
            continue;
          }

          // Check if conversation exists between sender and receiver
          let conversation = await this.findOrCreateConversation(uidFrom, uidTo);

          // Save the message to the conversation
          await this.saveMessageToConversation(conversation.id, data);
        }
      }

      // Clear processed messages
      this.messages = [];
      console.log("Messages processed and saved to database");
    } catch (error) {
      console.error("Error processing messages:", error);
    }
  }
  async getFriendRquest() {
    try {
      const friendRequest = await this.api.getFriendRequest();
      return friendRequest;
    } catch (error) {
      console.error("Error getting friend request:", error);
      throw error;
    }
  }
  async sendFriendRequest(userId) {
    try {
      const friendRequest = await this.api.sendFriendRequest(
        "Hello from Bot",
        userId
      );
      return friendRequest;
    } catch (error) {
      console.error("Error getting friend request:", error);
      throw error;
    }
  }
  async findUserByPhone(phoneNumber) {
    try {
      const user = await this.api.findUser(phoneNumber);
      return user;
    } catch (error) {
      console.error("Error finding user:", error);
      throw error;
    }
  }
  async acceptFriendRequest(userId) {
    try {
      const friendRequest = await this.api.acceptFriendRequest(userId);
      return friendRequest;
    } catch (error) {
      console.error("Error accepting friend request:", error);
      throw error;
    }
  }

  async findOrCreateConversation(uidFrom, uidTo) {
    try {
      // First, try to find existing conversation in either direction
      let conversation = await this.prisma.conversation.findFirst({
        where: {
          OR: [
            { uidFrom: uidFrom, uidTo: uidTo },
            { uidFrom: uidTo, uidTo: uidFrom }
          ]
        }
      });

      // If no conversation exists, create a new one
      if (!conversation) {
        conversation = await this.prisma.conversation.create({
          data: {
            uidFrom: uidFrom,
            uidTo: uidTo
          }
        });
        console.log(`Created new conversation between ${uidFrom} and ${uidTo}`);
      } else {
        console.log(`Found existing conversation between ${uidFrom} and ${uidTo}`);
      }

      return conversation;
    } catch (error) {
      console.error("Error finding or creating conversation:", error);
      throw error;
    }
  }

  async saveMessageToConversation(conversationId, messageData) {
    try {
      const messageType = this.getMessageType(messageData.msgType);

      const message = await this.prisma.message.create({
        data: {
          conversationId: conversationId,
          content: messageData.content,
          type: messageType,
          uidFrom: messageData.uidFrom,
          uidTo: messageData.uidTo || messageData.idTo,
          msgId: messageData.msgId,
          msgType: messageData.msgType,
          isFromUser: messageData.uidFrom !== "0", // Assuming "0" means system/bot
          mediaUrl: this.extractMediaUrl(messageData.content),
          stickerId: this.extractStickerId(messageData.content)
        }
      });

      console.log(`Saved message ${messageData.msgId} to conversation ${conversationId}`);
      return message;
    } catch (error) {
      console.error("Error saving message:", error);
      throw error;
    }
  }

  getMessageType(msgType) {
    // Map Zalo message types to our enum
    switch (msgType) {
      case "chat.photo":
      case "chat.video":
      case "chat.file":
        return "MEDIA";
      case "chat.sticker":
        return "STICKER";
      default:
        return "TEXT";
    }
  }

  extractMediaUrl(content) {
    // Extract media URL from content if it's an object
    if (typeof content === 'object' && content !== null) {
      return content.href || content.url || null;
    }
    return null;
  }

  extractStickerId(content) {
    // Extract sticker ID from content if it's a sticker
    if (typeof content === 'object' && content !== null) {
      return content.stickerId || content.id || null;
    }
    return null;
  }

  async cleanup() {
    try {
      await this.prisma.$disconnect();
      console.log("Prisma client disconnected");
    } catch (error) {
      console.error("Error disconnecting Prisma client:", error);
    }
  }
}
