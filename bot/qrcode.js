// drawQr.ts
import * as fs from "fs/promises";
import { Jim<PERSON> } from "jimp";
import QrCode from "qrcode-reader";
import qrcodeTerminal from "qrcode-terminal";
import { access } from "fs/promises";
import { constants } from "fs";

/**
 * Asynchronously waits for a file to exist at the given path.
 *
 * @param {string} filePath The absolute or relative path to the file.
 * @param {number} [timeout=30000] The maximum time to wait in milliseconds.
 * @param {number} [checkInterval=1000] The interval at which to check for the file in milliseconds.
 * @returns {Promise<void>} A promise that resolves when the file is found.
 * @throws {Error} If the timeout is reached before the file is found.
 */
function waitForFile(filePath, timeout = 30000, checkInterval = 1000) {
  const startTime = Date.now();

  return new Promise((resolve, reject) => {
    const check = async () => {
      try {
        await access(filePath, constants.F_OK);
        resolve();
      } catch (error) {
        if (Date.now() - startTime > timeout) {
          reject(
            new Error(
              `Timeout: File "${filePath}" did not appear within ${timeout}ms.`
            )
          );
        } else {
          setTimeout(check, checkInterval);
        }
      }
    };

    check();
  });
}
/**
 * Reads a QR code image from a given file path, decodes it,
 * and then draws the content as a new QR code in the terminal.
 *
 * @param filePath The path to the QR code PNG file.
 */
export async function readAndDrawQrCodeInTerminal(filePath) {
  try {
    await waitForFile(filePath);
    // 1. Check if the file exists
    await fs.access(filePath);

    // 2. Read the image file buffer
    const buffer = await fs.readFile(filePath);

    // 3. Decode the QR code from the image buffer
    const image = await Jimp.read(buffer);
    const qrReader = new QrCode();

    const value = await new Promise((resolve, reject) => {
      qrReader.callback = (err, value) => {
        if (err) {
          return reject(err);
        }
        resolve(value);
      };
      qrReader.decode(image.bitmap);
    });

    // 4. Check for a valid result and draw to terminal
    if (value && value.result) {
      console.log(`Successfully decoded QR Code. Data: ${value.result}`);
      console.log("Drawing the decoded QR code in your terminal:");

      qrcodeTerminal.generate(value.result, { small: true });
    } else {
      console.error("Could not decode a QR code from the provided image.");
    }
  } catch (error) {
    if (error.code === "ENOENT") {
      console.error(`Error: File not found at path: ${filePath}`);
    } else {
      console.error("An unexpected error occurred:", error.message);
    }
  }
}
