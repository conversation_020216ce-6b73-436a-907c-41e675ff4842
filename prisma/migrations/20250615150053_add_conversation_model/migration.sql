/*
  Warnings:

  - Added the required column `conversationId` to the `Message` table without a default value. This is not possible if the table is not empty.
  - Added the required column `uidFrom` to the `Message` table without a default value. This is not possible if the table is not empty.
  - Added the required column `uidTo` to the `Message` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `content` on the `Message` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- DropForeignKey
ALTER TABLE "Message" DROP CONSTRAINT "Message_userId_fkey";

-- AlterTable
ALTER TABLE "Message" ADD COLUMN     "conversationId" INTEGER NOT NULL,
ADD COLUMN     "msgId" TEXT,
ADD COLUMN     "msgType" TEXT,
ADD COLUMN     "uidFrom" TEXT NOT NULL,
ADD COLUMN     "uidTo" TEXT NOT NULL,
ALTER COLUMN "userId" DROP NOT NULL,
DROP COLUMN "content",
ADD COLUMN     "content" JSONB NOT NULL;

-- CreateTable
CREATE TABLE "Conversation" (
    "id" SERIAL NOT NULL,
    "uidFrom" TEXT NOT NULL,
    "uidTo" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Conversation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Conversation_uidFrom_idx" ON "Conversation"("uidFrom");

-- CreateIndex
CREATE INDEX "Conversation_uidTo_idx" ON "Conversation"("uidTo");

-- CreateIndex
CREATE UNIQUE INDEX "Conversation_uidFrom_uidTo_key" ON "Conversation"("uidFrom", "uidTo");

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_uidFrom_fkey" FOREIGN KEY ("uidFrom") REFERENCES "User"("zaloUserId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_uidTo_fkey" FOREIGN KEY ("uidTo") REFERENCES "User"("zaloUserId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
