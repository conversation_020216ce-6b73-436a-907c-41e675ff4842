generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          Int            @id @default(autoincrement())
  zaloUserId  String         @unique
  imei        String
  userAgent   String
  cookie      String
  createdAt   DateTime       @default(now())
  messages    Message[]
  friendRequests FriendRequest[]
}

model Conversation {
  id        Int      @id @default(autoincrement())
  uidFrom   String   // Sender's Zalo user ID
  uidTo     String   // Receiver's Zalo user ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  messages  Message[]

  @@unique([uidFrom, uidTo])
  @@index([uidFrom])
  @@index([uidTo])
}

model Message {
  id             Int          @id @default(autoincrement())
  userId         Int?
  user           User?        @relation(fields: [userId], references: [id])
  conversationId Int
  conversation   Conversation @relation(fields: [conversationId], references: [id])
  content        Json         // Can be string or object (TAttachmentContent, TOtherContent)
  type           MessageType
  mediaUrl       String?
  stickerId      String?
  uidFrom        String       // Sender's Zalo user ID
  uidTo          String       // Receiver's Zalo user ID
  msgId          String?      // Original Zalo message ID
  msgType        String?      // Original Zalo message type
  isFromUser     Boolean
  createdAt      DateTime     @default(now())
}

model FriendRequest {
  id         Int               @id @default(autoincrement())
  userId     Int
  user       User              @relation(fields: [userId], references: [id])
  fromUserId String
  message    String?
  status     FriendRequestStatus
  createdAt  DateTime          @default(now())
  updatedAt  DateTime          @updatedAt
}

enum MessageType {
  TEXT
  MEDIA
  STICKER
}

enum FriendRequestStatus {
  PENDING
  ACCEPTED
  REJECTED
}
