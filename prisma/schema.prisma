generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          Int            @id @default(autoincrement())
  zaloUserId  String         @unique
  imei        String
  userAgent   String
  cookie      String
  createdAt   DateTime       @default(now())
  messages    Message[]
  friendRequests FriendRequest[]
  sentConversations Conversation[] @relation("SenderConversations")
  receivedConversations Conversation[] @relation("ReceiverConversations")
}

model Conversation {
  id        Int      @id @default(autoincrement())
  uidFrom   String   // Sender's Zalo user ID
  uidTo     String   // Receiver's Zalo user ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  messages  Message[]

  // Relations to User model (optional, for when we have users in our database)
  sender    User?    @relation("SenderConversations", fields: [uidFrom], references: [zaloUserId])
  receiver  User?    @relation("ReceiverConversations", fields: [uidTo], references: [zaloUserId])

  @@unique([uidFrom, uidTo])
  @@index([uidFrom])
  @@index([uidTo])
}

model Message {
  id             Int          @id @default(autoincrement())
  userId         Int?
  user           User?        @relation(fields: [userId], references: [id])
  conversationId Int
  conversation   Conversation @relation(fields: [conversationId], references: [id])
  content        String
  type           MessageType
  mediaUrl       String?
  stickerId      String?
  uidFrom        String       // Sender's Zalo user ID
  uidTo          String       // Receiver's Zalo user ID
  msgId          String?      // Original Zalo message ID
  isFromUser     Boolean
  createdAt      DateTime     @default(now())
}

model FriendRequest {
  id         Int               @id @default(autoincrement())
  userId     Int
  user       User              @relation(fields: [userId], references: [id])
  fromUserId String
  message    String?
  status     FriendRequestStatus
  createdAt  DateTime          @default(now())
  updatedAt  DateTime          @updatedAt
}

enum MessageType {
  TEXT
  MEDIA
  STICKER
}

enum FriendRequestStatus {
  PENDING
  ACCEPTED
  REJECTED
}
